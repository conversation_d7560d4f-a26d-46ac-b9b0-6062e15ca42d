#include "scheduler.h"

// ������������
uint8_t task_num;

// ����ṹ�嶨��
typedef struct {
    void (*task_func)(void);  // ������ָ��
    uint32_t rate_ms;         // ����ִ��Ƶ��(����)
    uint32_t last_run;        // �ϴ�ִ��ʱ���
} task_t;

u32 i=0;


void uart_proc(void)
{
	
	my_printf(&huart1,"{a}%f,%d\r\n",vol_amp2/2*10,output); 
}

// �����б����飬����������Ҫ���ȵ�����
static task_t scheduler_task[] =
{
    {ad_proc, 1, 0},         // AD�ɼ���������ÿ1msִ��һ��
    //{DA_proc, 10, 0},        // DA�����������ÿ10msִ��һ��
    //{fft_proc, 5000, 0},     // FFT��������ÿ1000ms(1��)ִ��һ��
    {AD9959_proc, 1200, 0},    // AD9959��������ÿ10msִ��һ��
    {uart_proc, 10, 0},  // ���ڲ�������ÿ1000ms(1��)ִ��һ��
		{Pid_Proc,1,0},
};


/**
 * @brief ��������ʼ������
 */
void scheduler_init(void)
{
    // ������������
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}


/**
 * @brief ���������к�����ѭ����鲢ִ�е��ڵ�����
 */
void scheduler_run(void)
{
    for (uint8_t i = 0; i < task_num; i++)
    {
        // ��ȡ��ǰϵͳʱ��(����)
        uint32_t now_time = HAL_GetTick();

        // �ж������Ƿ񵽴�ִ��ʱ��
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            // ���������ϴ�ִ��ʱ��
            scheduler_task[i].last_run = now_time;

            // ִ��������
            scheduler_task[i].task_func();
        }
    }
}
